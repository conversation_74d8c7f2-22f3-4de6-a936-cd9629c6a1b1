/* Enhanced Card Styles */

/* === BASE CARD STYLES === */
.enhanced-card {
  position: relative;
  transition: var(--transition-normal);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.enhanced-card > div[class*="Polaris-Card"] {
  border-radius: var(--radius-lg);
  transition: var(--transition-normal);
}

/* === CARD VARIANTS === */
.enhanced-card--default > div[class*="Polaris-Card"] {
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
}

.enhanced-card--elevated > div[class*="Polaris-Card"] {
  border: none;
  box-shadow: var(--shadow-md);
  background: var(--color-surface);
}

.enhanced-card--outlined > div[class*="Polaris-Card"] {
  border: 2px solid var(--color-border);
  box-shadow: none;
  background: var(--color-surface);
}

.enhanced-card--subtle > div[class*="Polaris-Card"] {
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-xs);
  background: var(--color-background-secondary);
}

.enhanced-card--success > div[class*="Polaris-Card"] {
  border: 1px solid var(--color-success-200);
  box-shadow: var(--shadow-sm);
  background: var(--color-success-50);
}

.enhanced-card--warning > div[class*="Polaris-Card"] {
  border: 1px solid var(--color-warning-200);
  box-shadow: var(--shadow-sm);
  background: var(--color-warning-50);
}

.enhanced-card--error > div[class*="Polaris-Card"] {
  border: 1px solid var(--color-error-200);
  box-shadow: var(--shadow-sm);
  background: var(--color-error-50);
}

/* === INTERACTIVE STATES === */
.enhanced-card--interactive {
  cursor: pointer;
}

.enhanced-card--interactive:hover > div[class*="Polaris-Card"] {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-border-hover);
}

.enhanced-card--interactive:active > div[class*="Polaris-Card"] {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.enhanced-card--interactive:focus-within > div[class*="Polaris-Card"] {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* === LOADING STATE === */
.enhanced-card--loading {
  pointer-events: none;
  opacity: 0.7;
}

.enhanced-card__loading {
  padding: var(--space-lg);
}

/* === TEMPLATE CARD STYLES === */
.template-card {
  height: 100%;
  min-width: 300px;
  max-width: 300px;
  cursor: pointer;
}

.template-card__content {
  padding: var(--space-xl);
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.template-card__emoji {
  font-size: var(--font-size-3xl);
  line-height: 1;
  margin-bottom: var(--space-sm);
}

.template-card__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-xs);
}

.template-card__description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.template-card__tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
  margin-top: auto;
}

.template-card__tag {
  background: var(--color-primary-100);
  color: var(--color-primary-700);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

/* === STATS CARD STYLES === */
.stats-card__content {
  padding: var(--space-xl);
}

.stats-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.stats-card__title {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.stats-card__icon {
  color: var(--color-text-tertiary);
}

.stats-card__value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-sm);
}

.stats-card__change {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.stats-card__change--positive {
  color: var(--color-success-600);
}

.stats-card__change--negative {
  color: var(--color-error-600);
}

.stats-card__change--neutral {
  color: var(--color-text-tertiary);
}

/* === ACTION CARD STYLES === */
.action-card__content {
  padding: var(--space-xl);
  display: flex;
  align-items: center;
  gap: var(--space-lg);
}

.action-card__icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  background: var(--color-primary-100);
  color: var(--color-primary-600);
}

.action-card--success .action-card__icon {
  background: var(--color-success-100);
  color: var(--color-success-600);
}

.action-card--warning .action-card__icon {
  background: var(--color-warning-100);
  color: var(--color-warning-600);
}

.action-card--error .action-card__icon {
  background: var(--color-error-100);
  color: var(--color-error-600);
}

.action-card__text {
  flex-grow: 1;
}

.action-card__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-xs);
}

.action-card__description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
}

.action-card__button {
  flex-shrink: 0;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  .template-card__content {
    padding: var(--space-lg);
  }
  
  .stats-card__content {
    padding: var(--space-lg);
  }
  
  .action-card__content {
    padding: var(--space-lg);
    flex-direction: column;
    text-align: center;
    gap: var(--space-md);
  }
  
  .action-card__icon {
    width: 40px;
    height: 40px;
  }
}

/* === ACCESSIBILITY === */
.enhanced-card--interactive:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  .enhanced-card,
  .enhanced-card > div[class*="Polaris-Card"] {
    transition: none;
  }
  
  .enhanced-card--interactive:hover > div[class*="Polaris-Card"] {
    transform: none;
  }
}

/* === DARK MODE PREPARATION === */
@media (prefers-color-scheme: dark) {
  .enhanced-card--default > div[class*="Polaris-Card"],
  .enhanced-card--elevated > div[class*="Polaris-Card"],
  .enhanced-card--outlined > div[class*="Polaris-Card"],
  .enhanced-card--subtle > div[class*="Polaris-Card"] {
    /* Dark mode styles will be added when implementing dark mode */
  }
}
